{"template_name": "默认商务模板", "description": "现代简约的商务PPT模板，适用于各种商务演示场景。采用深色背景和蓝色主色调，支持多种内容类型展示。", "html_template": "<!DOCTYPE html>\n<html lang=\"zh-CN\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>{{ page_title }}</title>\n    <script src=\"https://cdn.tailwindcss.com\"></script>\n    <script src=\"https://cdn.jsdelivr.net/npm/chart.js\"></script>\n    <script src=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/js/all.min.js\"></script>\n    <style>\n        html {\n            height: 100%;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            background-color: #111827; \n        }\n\n        body {\n            width: 1280px;\n            height: 720px;\n            margin: 0;\n            padding: 0;\n            position: relative;\n            overflow: hidden;\n            background: linear-gradient(135deg, #1e293b 0%, #334155 100%);\n            font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;\n            flex-shrink: 0; \n        }\n        \n        .slide-container {\n            width: 100%;\n            height: 100%;\n            display: flex;\n            flex-direction: column;\n            color: white;\n            position: relative;\n        }\n        \n        .slide-header {\n            padding: 40px 60px 20px 60px;\n            border-bottom: 2px solid rgba(96, 165, 250, 0.3);\n        }\n        \n        .slide-title {\n            font-size: clamp(2rem, 4vw, 3.5rem);\n            font-weight: bold;\n            color: #60a5fa;\n            margin: 0;\n            line-height: 1.2;\n            max-height: 80px;\n            overflow: hidden;\n        }\n        \n        .slide-content {\n            flex: 1;\n            padding: 30px 60px;\n            display: flex;\n            flex-direction: column;\n            justify-content: center;\n            max-height: 580px;\n            overflow: hidden;\n        }\n        \n        .content-main {\n            font-size: clamp(1rem, 2.5vw, 1.4rem);\n            line-height: 1.5;\n            color: #e2e8f0;\n        }\n        \n        .content-points {\n            list-style: none;\n            padding: 0;\n            margin: 0;\n        }\n        \n        .content-points li {\n            margin-bottom: 15px;\n            padding-left: 30px;\n            position: relative;\n        }\n        \n        .content-points li:before {\n            content: \"▶\";\n            position: absolute;\n            left: 0;\n            color: #60a5fa;\n            font-size: 0.8em;\n        }\n        \n        .slide-footer {\n            position: absolute;\n            bottom: 20px;\n            right: 30px;\n            font-size: 14px;\n            color: #94a3b8;\n            font-weight: 600;\n        }\n        \n        .chart-container {\n            max-height: 300px;\n            margin: 20px 0;\n        }\n        \n        .highlight-box {\n            background: rgba(96, 165, 250, 0.1);\n            border-left: 4px solid #60a5fa;\n            padding: 20px;\n            margin: 20px 0;\n            border-radius: 0 8px 8px 0;\n        }\n        \n        .stats-grid {\n            display: grid;\n            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n            gap: 20px;\n            margin: 20px 0;\n        }\n        \n        .stat-card {\n            background: rgba(255, 255, 255, 0.1);\n            padding: 20px;\n            border-radius: 8px;\n            text-align: center;\n            border: 1px solid rgba(96, 165, 250, 0.3);\n        }\n        \n        .stat-number {\n            font-size: 2.5rem;\n            font-weight: bold;\n            color: #60a5fa;\n            display: block;\n        }\n        \n        .stat-label {\n            font-size: 1rem;\n            color: #cbd5e1;\n            margin-top: 5px;\n        }\n        \n        @media (max-width: 1280px) {\n            body {\n                width: 100vw;\n                height: 56.25vw;\n                max-height: 100vh;\n            }\n        }\n    </style>\n</head>\n<body>\n    <div class=\"slide-container\">\n        <div class=\"slide-header\">\n            <h1 class=\"slide-title\">{{ main_heading }}</h1>\n        </div>\n        \n        <div class=\"slide-content\">\n            <div class=\"content-main\">\n                {{ page_content }}\n            </div>\n        </div>\n        \n        <div class=\"slide-footer\">\n            {{ current_page_number }} / {{ total_page_count }}\n        </div>\n    </div>\n</body>\n</html>", "tags": ["默认", "商务", "现代", "简约", "深色"], "is_default": false, "export_info": {"exported_at": "2025-06-28T10:11:20.488Z", "original_id": 1, "original_created_at": 1749553414.0671556}}