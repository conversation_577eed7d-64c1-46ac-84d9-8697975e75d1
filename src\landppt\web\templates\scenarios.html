{% extends "base.html" %}

{% block title %}场景选择 - LandPPT{% endblock %}

{% block extra_css %}
<style>
/* 紧凑型场景选择页面 */
.scenarios-hero {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    color: var(--text-primary);
    padding: var(--spacing-lg) var(--spacing-md);
    margin: calc(-1 * var(--spacing-lg)) calc(-1 * var(--spacing-lg)) var(--spacing-lg) calc(-1 * var(--spacing-lg));
    text-align: center;
    position: relative;
    overflow: hidden;
    border-radius: 0 0 var(--border-radius-lg) var(--border-radius-lg);
}

.scenarios-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 30% 20%, rgba(102, 126, 234, 0.08) 0%, transparent 50%),
        radial-gradient(circle at 70% 80%, rgba(118, 75, 162, 0.08) 0%, transparent 50%);
    animation: scenarioFloat 15s ease-in-out infinite;
}

@keyframes scenarioFloat {
    0%, 100% { transform: translateX(0px) rotate(0deg); }
    33% { transform: translateX(5px) rotate(0.5deg); }
    66% { transform: translateX(-3px) rotate(-0.5deg); }
}

.scenarios-hero h2 {
    font-size: 2.2rem;
    font-weight: 700;
    margin-bottom: var(--spacing-md);
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
    z-index: 1;
    letter-spacing: -0.02em;
}

.scenarios-hero p {
    font-size: 1rem;
    color: var(--text-secondary);
    max-width: 500px;
    margin: 0 auto;
    position: relative;
    z-index: 1;
    font-weight: 400;
    line-height: 1.5;
}

/* 适中尺寸的场景卡片 */
.scenarios-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
}

.scenario-card {
    background: var(--glass-bg);
    backdrop-filter: blur(15px);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-md);
    box-shadow: 0 4px 16px rgba(31, 38, 135, 0.18);
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    position: relative;
    height: fit-content;
}

.scenario-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--primary-gradient);
    transform: scaleX(0);
    transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 1;
}

.scenario-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05), rgba(118, 75, 162, 0.05));
    opacity: 0;
    transition: opacity 0.4s ease;
}

.scenario-card:hover::before {
    transform: scaleX(1);
}

.scenario-card:hover::after {
    opacity: 1;
}

.scenario-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 28px rgba(31, 38, 135, 0.25);
}

.scenario-icon {
    font-size: 2.8rem;
    margin: var(--spacing-lg) 0 var(--spacing-md) 0;
    filter: drop-shadow(0 2px 6px rgba(102, 126, 234, 0.15));
    transition: all 0.3s ease;
    position: relative;
    z-index: 2;
}

.scenario-card:hover .scenario-icon {
    transform: scale(1.05) rotate(5deg);
    filter: drop-shadow(0 4px 8px rgba(102, 126, 234, 0.25));
}

.scenario-title {
    color: var(--text-primary);
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: var(--spacing-sm);
    line-height: 1.3;
    position: relative;
    z-index: 2;
    padding: 0 var(--spacing-md);
}

.scenario-description {
    color: var(--text-secondary);
    line-height: 1.4;
    padding: 0 var(--spacing-md);
    margin-bottom: var(--spacing-lg);
    font-size: 0.9rem;
    position: relative;
    z-index: 2;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.scenario-button {
    background: var(--primary-gradient);
    color: white;
    border: none;
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--border-radius-md);
    font-weight: 600;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    margin-bottom: var(--spacing-lg);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.25);
    position: relative;
    z-index: 2;
    overflow: hidden;
}

.scenario-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.scenario-button:hover {
    transform: translateY(-2px) scale(1.02);
    box-shadow: 0 6px 18px rgba(102, 126, 234, 0.35);
}

.scenario-button:hover::before {
    left: 100%;
}

/* 适中尺寸的响应式设计 */
@media (max-width: 768px) {
    .scenarios-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: var(--spacing-sm);
        padding: 0 var(--spacing-sm);
    }

    .scenarios-hero {
        padding: var(--spacing-md) var(--spacing-sm);
        margin: calc(-1 * var(--spacing-lg)) calc(-1 * var(--spacing-sm)) var(--spacing-md) calc(-1 * var(--spacing-sm));
    }

    .scenarios-hero h2 {
        font-size: 1.8rem;
    }

    .scenarios-hero p {
        font-size: 0.9rem;
    }

    .scenario-icon {
        font-size: 2.2rem;
        margin: var(--spacing-md) 0 var(--spacing-sm) 0;
    }

    .scenario-title {
        font-size: 1rem;
        padding: 0 var(--spacing-sm);
    }

    .scenario-description {
        font-size: 0.8rem;
        padding: 0 var(--spacing-sm);
        margin-bottom: var(--spacing-md);
        -webkit-line-clamp: 2;
    }

    .scenario-button {
        padding: var(--spacing-xs) var(--spacing-md);
        font-size: 0.85rem;
        margin-bottom: var(--spacing-md);
    }
}

@media (max-width: 480px) {
    .scenarios-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-xs);
    }

    .scenario-card {
        border-radius: var(--border-radius-sm);
    }

    .scenario-icon {
        font-size: 1.8rem;
        margin: var(--spacing-sm) 0 var(--spacing-xs) 0;
    }

    .scenario-title {
        font-size: 0.9rem;
        padding: 0 var(--spacing-xs);
    }

    .scenario-description {
        font-size: 0.75rem;
        margin-bottom: var(--spacing-sm);
        padding: 0 var(--spacing-xs);
        -webkit-line-clamp: 1;
    }

    .scenario-button {
        padding: var(--spacing-xs) var(--spacing-sm);
        font-size: 0.8rem;
        margin-bottom: var(--spacing-sm);
    }
}
</style>
{% endblock %}

{% block content %}
<div class="scenarios-hero">
    <h2>🎨 选择 PPT 场景</h2>
    <p>选择最适合您需求的场景模板，AI 将根据场景特点生成专业的 PPT</p>
</div>

<div class="scenarios-grid">
    {% for scenario in scenarios %}
    <div class="scenario-card" onclick="selectScenario('{{ scenario.id }}', '{{ scenario.name }}')">
        <div style="text-align: center;">
            <div class="scenario-icon">{{ scenario.icon }}</div>
            <h3 class="scenario-title">{{ scenario.name }}</h3>
            <p class="scenario-description">{{ scenario.description }}</p>
            <button class="scenario-button" onclick="event.stopPropagation(); selectScenario('{{ scenario.id }}', '{{ scenario.name }}')">
                选择此场景
            </button>
        </div>
    </div>
    {% endfor %}
</div>

<!-- PPT Generation Form Modal -->
<div id="generationModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.7); z-index: 1000; backdrop-filter: blur(5px);">
    <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 40px; border-radius: 20px; width: 90%; max-width: 600px; max-height: 90vh; overflow-y: auto; box-shadow: 0 20px 60px rgba(0,0,0,0.3);">
        <h3 style="color: #2c3e50; margin-bottom: 20px; text-align: center; font-weight: 600;">
            🎨 创建 <span id="selectedScenarioName" style="color: #667eea;"></span> 项目
        </h3>
        
        <form action="/projects/create" method="post">
            <input type="hidden" id="scenarioInput" name="scenario" value="">
            
            <div class="form-group">
                <label for="topic">PPT 主题 *</label>
                <input type="text" id="topic" name="topic" required placeholder="例如：人工智能技术发展趋势">
            </div>
            
            <div class="form-group">
                <label for="requirements">具体要求</label>
                <textarea id="requirements" name="requirements" rows="4" placeholder="请描述您的具体要求，如重点内容、风格偏好等..."></textarea>
            </div>
            
            <div class="form-group">
                <label for="language">语言</label>
                <select id="language" name="language">
                    <option value="zh">中文</option>
                    <option value="en">English</option>
                </select>
            </div>
            
            <div class="form-group">
                <label style="display: flex; align-items: center; cursor: pointer;">
                    <input type="checkbox" id="network_mode" name="network_mode" style="margin-right: 10px; width: auto;">
                    <span style="display: flex; flex-direction: column;">
                        <span>🔍 启用Research联网模式</span>
                        <small style="color: #7f8c8d; margin-top: 5px;">
                            使用AI进行联网搜索，获取权威信息源，生成研究报告并保存到本地
                        </small>
                    </span>
                </label>
            </div>
            
            <div style="text-align: center; margin-top: 30px;">
                <button type="button" onclick="closeModal()" class="btn" style="background: #95a5a6; margin-right: 10px;">取消</button>
                <button type="submit" class="btn btn-primary">📋 创建项目</button>
            </div>
        </form>
    </div>
</div>


{% endblock %}

{% block extra_js %}
<script>
function selectScenario(scenarioId, scenarioName) {
    document.getElementById('scenarioInput').value = scenarioId;
    document.getElementById('selectedScenarioName').textContent = scenarioName;
    document.getElementById('generationModal').style.display = 'block';
    
    // Set focus to topic input
    setTimeout(() => {
        document.getElementById('topic').focus();
    }, 100);
}

function closeModal() {
    document.getElementById('generationModal').style.display = 'none';
}

// Close modal when clicking outside
document.getElementById('generationModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeModal();
    }
});

// Close modal with Escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeModal();
    }
});
</script>
{% endblock %}
