<svg width="48" height="48" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4facfe;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#00f2fe;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="0" dy="2" stdDeviation="4" flood-color="rgba(0,0,0,0.25)"/>
    </filter>
  </defs>

  <!-- 背景圆角矩形 - 半透明融合效果 -->
  <rect x="2" y="2" width="44" height="44" rx="12" ry="12" fill="url(#primaryGradient)" opacity="0.15" filter="url(#shadow)"/>
  <!-- 边框增强视觉效果 -->
  <rect x="2" y="2" width="44" height="44" rx="12" ry="12" fill="none" stroke="url(#primaryGradient)" stroke-width="1" opacity="0.3"/>

  <!-- 无缝融合的圆角 LP 字母组合 -->
  <g transform="translate(6, 10)">
    <!-- L的垂直线 - 圆角，使用渐变色 -->
    <rect x="2" y="4" width="3" height="20" rx="1.5" fill="url(#primaryGradient)"/>
    <!-- L的底部横线，延伸到P - 圆角，使用渐变色 -->
    <rect x="2" y="21" width="18" height="3" rx="1.5" fill="url(#primaryGradient)"/>

    <!-- P的垂直线，与L共享 - 圆角，使用渐变色 -->
    <rect x="14" y="4" width="3" height="14" rx="1.5" fill="url(#primaryGradient)"/>
    <!-- P的上横线 - 圆角，使用渐变色 -->
    <rect x="14" y="4" width="12" height="3" rx="1.5" fill="url(#primaryGradient)"/>
    <!-- P的中横线 - 圆角，使用渐变色 -->
    <rect x="14" y="11" width="12" height="3" rx="1.5" fill="url(#primaryGradient)"/>
    <!-- P的右侧弧形 - 更圆润，使用渐变色 -->
    <path d="M 26 4 L 30 4 C 33.5 4 36 6.5 36 9 C 36 11.5 33.5 14 30 14 L 26 14 Z"
          fill="url(#primaryGradient)"/>
  </g>


  <!-- 底部装饰线 -->
  <rect x="12" y="35" width="24" height="1" rx="0.5" fill="white" opacity="0.3"/>
</svg>
