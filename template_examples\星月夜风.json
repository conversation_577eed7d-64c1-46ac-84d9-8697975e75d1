{"template_name": "星月夜风", "description": "灵感源于梵高的《星月夜》。通过模拟流动的油画笔触、强烈的色彩对比和手写艺术字体，营造出极具视觉冲击力和情感张力的梦幻风格。", "html_template": "<!DOCTYPE html>\n<html lang=\"zh-CN\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>{{ page_title }}</title>\n    <link rel=\"preconnect\" href=\"https://fonts.googleapis.com\">\n    <link rel=\"preconnect\" href=\"https://fonts.gstatic.com\" crossorigin>\n    <link href=\"https://fonts.googleapis.com/css2?family=Lobster&display=swap\" rel=\"stylesheet\">\n    <script src=\"https://cdn.tailwindcss.com\"></script>\n    <script src=\"https://cdn.jsdelivr.net/npm/chart.js\"></script>\n    <script src=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/js/all.min.js\"></script>\n    <style>\n        html, body {\n            width: 100%;\n            height: 100%;\n            margin: 0;\n            padding: 0;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            background-color: #010410;\n            overflow: hidden;\n        }\n\n        .background-canvas {\n            position: absolute;\n            top: 0; left: 0; right: 0; bottom: 0;\n            background: linear-gradient(135deg, #010410, #03102a, #0b2a54, #010410);\n            z-index: 1;\n        }\n\n        .background-canvas::before, .background-canvas::after {\n            content: '';\n            position: absolute;\n            left: 0; top: 0; right: 0; bottom: 0;\n            background: radial-gradient(circle at 20% 25%, #f9d71c, transparent 20%),\n                        radial-gradient(circle at 80% 75%, #4a90e2, transparent 25%);\n            mix-blend-mode: color-dodge;\n            animation: paint-swirl 20s ease-in-out infinite alternate;\n        }\n\n        .background-canvas::after {\n            background: radial-gradient(circle at 50% 50%, #ffffff, transparent 15%),\n                        radial-gradient(circle at 10% 80%, #f9d71c, transparent 20%);\n            animation-delay: -10s;\n            mix-blend-mode: overlay;\n        }\n\n        #turbulence {\n            position: absolute;\n            width: 100%; height: 100%;\n            filter: url(#paint-turbulence);\n            opacity: 0.8;\n        }\n\n        @keyframes paint-swirl {\n            from { transform: rotate(0deg) scale(1.5); opacity: 0.6; }\n            to { transform: rotate(180deg) scale(1); opacity: 1; }\n        }\n\n        .slide-container {\n            width: 1280px;\n            height: 720px;\n            display: flex;\n            flex-direction: column;\n            color: #e0e6f1;\n            position: relative;\n            z-index: 3; \n            background: rgba(8, 23, 53, 0.4);\n            backdrop-filter: blur(8px) brightness(1.1);\n            -webkit-backdrop-filter: blur(8px) brightness(1.1);\n            border: 1.5px solid rgba(137, 207, 240, 0.3);\n            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.6);\n            clip-path: polygon(0 20px, 20px 0, calc(100% - 20px) 0, 100% 20px, 100% calc(100% - 20px), calc(100% - 20px) 100%, 20px 100%, 0 calc(100% - 20px));\n        }\n        \n        .slide-header {\n            padding: 40px 60px 20px 60px;\n            border-bottom: 1px solid transparent;\n            border-image: linear-gradient(to right, rgba(255, 215, 0, 0.5), transparent) 1;\n        }\n        \n        .slide-title {\n            font-family: 'Lobster', 'Georgia', cursive;\n            font-size: clamp(3rem, 5vw, 4.5rem);\n            font-weight: 400;\n            line-height: 1.2;\n            text-align: left;\n            color: #FFD700;\n        }\n        \n        .slide-content {\n            flex: 1;\n            padding: 30px 60px;\n            display: flex;\n            flex-direction: column;\n            justify-content: center;\n            max-height: 580px;\n            overflow-y: auto;\n            font-family: 'Helvetica Neue', 'Arial', sans-serif;\n        }\n        \n        .content-main {\n            font-size: clamp(1.1rem, 2.5vw, 1.4rem);\n            line-height: 1.8; \n            color: #d0d8e8;\n            text-shadow: 1px 1px 2px rgba(0,0,0,0.7);\n        }\n        \n        .content-points {\n            list-style: none; padding: 0; margin: 25px 0 0 0;\n        }\n        \n        .content-points li {\n            margin-bottom: 25px; padding-left: 45px; position: relative; font-size: 1.25rem;\n        }\n        \n        .content-points li::before {\n            content: \"\\2605\";\n            position: absolute;\n            left: 5px; top: 8px;\n            font-size: 20px;\n            color: #FFD700;\n            animation: shooting-star 3s ease-in-out infinite;\n        }\n\n        @keyframes shooting-star {\n            0% { transform: translateX(0) scale(1); opacity: 1; text-shadow: 0 0 8px #ffd700; }\n            40% { transform: translateX(5px) scale(1.2); opacity: 0.7; }\n            100% { transform: translateX(0) scale(1); opacity: 1; text-shadow: 0 0 8px #ffd700; }\n        }\n        \n        .slide-footer {\n            position: absolute;\n            bottom: 25px;\n            right: 40px;\n            font-size: 16px;\n            color: rgba(255, 255, 255, 0.5);\n            font-weight: 600;\n            text-shadow: 1px 1px 2px #000;\n        }\n        \n        .highlight-box {\n            background: rgba(255, 215, 0, 0.08);\n            border-left: 4px solid #ffd700;\n            padding: 20px 25px;\n            margin: 20px 0;\n            clip-path: polygon(0 0, 100% 10%, 100% 100%, 0 90%);\n        }\n        \n        .stats-grid {\n            display: grid;\n            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n            gap: 30px;\n            margin: 20px 0;\n        }\n        \n        .stat-card {\n            background: transparent;\n            padding: 25px 20px;\n            text-align: center;\n            position: relative;\n            border: 2px solid rgba(137, 207, 240, 0.4);\n            transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);\n            clip-path: polygon(10% 0, 100% 0, 90% 100%, 0% 100%);\n        }\n\n        .stat-card:hover {\n            transform: translateY(-10px) scale(1.05) rotate(-2deg);\n            border-color: rgba(255, 215, 0, 0.8);\n            box-shadow: 0 0 30px rgba(255, 215, 0, 0.5);\n        }\n        \n        .stat-number {\n            font-family: 'Lobster', cursive;\n            font-size: 3rem;\n            font-weight: 400;\n            color: #FFD700;\n            display: block;\n            text-shadow: 0 0 15px rgba(255, 215, 0, 0.8);\n        }\n        \n        .stat-label {\n            font-size: 1rem;\n            color: rgba(255, 255, 255, 0.8);\n            margin-top: 5px;\n        }\n\n    </style>\n</head>\n<body>\n    <div class=\"background-canvas\">\n        <div id=\"turbulence\"></div>\n    </div>\n    \n    <div class=\"slide-container\">\n        <div class=\"slide-header\">\n            <h1 class=\"slide-title\">{{ main_heading }}</h1>\n        </div>\n        \n        <div class=\"slide-content\">\n            <div class=\"content-main\">\n                {{ page_content }}\n            </div>\n        </div>\n        \n        <div class=\"slide-footer\">\n            {{ current_page_number }} / {{ total_page_count }}\n        </div>\n    </div>\n\n    <svg xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\" style=\"display:none\">\n        <defs>\n            <filter id=\"paint-turbulence\">\n                <feTurbulence type=\"turbulence\" baseFrequency=\"0.01 0.02\" numOctaves=\"3\" seed=\"2\" stitchTiles=\"stitch\" result=\"turbulence\"/>\n                <feDisplacementMap in2=\"turbulence\" in=\"SourceGraphic\" scale=\"50\" xChannelSelector=\"R\" yChannelSelector=\"G\"/>\n                 <feGaussianBlur stdDeviation=\"2\" />\n            </filter>\n        </defs>\n    </svg>\n\n</body>\n</html>", "tags": ["星空", "梵高", "艺术", "油画", "动态", "梦幻", "强烈"], "is_default": false, "export_info": {"exported_at": "2025-07-23T08:40:00.000Z", "original_id": null, "original_created_at": null}}