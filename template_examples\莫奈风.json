{"template_name": "莫奈风", "description": "通过模拟油画笔触的动态背景、鲜明色彩的碰撞和光影文字，营造出极致梦幻、生动鲜活的印象派艺术风格。", "html_template": "<!DOCTYPE html>\n<html lang=\"zh-CN\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>{{ page_title }}</title>\n    <link rel=\"preconnect\" href=\"https://fonts.googleapis.com\">\n    <link rel=\"preconnect\" href=\"https://fonts.gstatic.com\" crossorigin>\n    <link href=\"https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;600&display=swap\" rel=\"stylesheet\">\n    <script src=\"https://cdn.tailwindcss.com\"></script>\n    <script src=\"https://cdn.jsdelivr.net/npm/chart.js\"></script>\n    <script src=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/js/all.min.js\"></script>\n    <style>\n        html, body {\n            width: 100%;\n            height: 100%;\n            margin: 0;\n            padding: 0;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            background-color: #e4d8c8;\n            overflow: hidden;\n        }\n\n        .background-canvas {\n            position: absolute;\n            top: 0; left: 0; right: 0; bottom: 0;\n            filter: url(#impressionist-turbulence);\n            background: linear-gradient(120deg, #a1c4fd 0%, #c2e9fb 25%, #fddb92 50%, #fbc2eb 75%, #a1c4fd 100%);\n            animation: color-flow 40s linear infinite;\n            background-size: 200% 200%;\n            z-index: 1;\n        }\n\n        @keyframes color-flow {\n            0% { background-position: 0% 50%; }\n            50% { background-position: 100% 50%; }\n            100% { background-position: 0% 50%; }\n        }\n\n        .slide-container {\n            width: 1280px;\n            height: 720px;\n            display: flex;\n            flex-direction: column;\n            color: #2c3a47;\n            position: relative;\n            z-index: 3; \n            background: rgba(255, 255, 255, 0.7);\n            backdrop-filter: blur(10px) saturate(110%);\n            -webkit-backdrop-filter: blur(10px) saturate(110%);\n            border: 1px solid rgba(255, 255, 255, 0.8);\n            box-shadow: 0 20px 60px rgba(100, 100, 150, 0.3);\n            -webkit-mask-image: url('data:image/svg+xml;charset=utf-8,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 1280 720\" preserveAspectRatio=\"none\"><path d=\"M20 0 H1260 C1271 0 1280 9 1280 20 V700 C1280 711 1271 720 1260 720 H20 C9 720 0 711 0 700 V20 C0 9 9 0 20 0Z\" fill=\"black\" /></svg>');\n            mask-border-source: url('data:image/svg+xml;charset=utf-8,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 1280 720\" preserveAspectRatio=\"none\"><path d=\"M20 0 H1260 C1271 0 1280 9 1280 20 V700 C1280 711 1271 720 1260 720 H20 C9 720 0 711 0 700 V20 C0 9 9 0 20 0Z\" fill=\"black\" /></svg>');\n            -webkit-mask-box-image: url('data:image/svg+xml;charset=utf-8,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><path d=\"M50,3 C23.9,3 3,23.9 3,50 C3,76.1 23.9,97 50,97 C76.1,97 97,76.1 97,50 C97,23.9 76.1,3 50,3Z\" stroke-width=\"6\" stroke=\"%23000\" fill=\"none\"/></svg>') 30 round;\n        }\n\n        .slide-header {\n            padding: 45px 60px 25px 60px;\n            border-bottom: 1px solid transparent;\n            background: linear-gradient(to bottom, rgba(255,255,255,0.5), transparent);\n        }\n        \n        .slide-title {\n            font-family: 'Playfair Display', 'Garamond', serif;\n            font-size: clamp(3rem, 5vw, 4.5rem);\n            font-weight: 600;\n            line-height: 1.3;\n            background: linear-gradient(60deg, #6a82fb, #fc5c7d);\n            -webkit-background-clip: text;\n            -webkit-text-fill-color: transparent;\n            filter: drop-shadow(2px 2px 3px rgba(0,0,0,0.1));\n        }\n        \n        .slide-content {\n            flex: 1;\n            padding: 30px 60px;\n            display: flex;\n            flex-direction: column;\n            justify-content: center;\n            max-height: 570px;\n            overflow-y: auto;\n            font-family: 'Helvetica Neue', 'Arial', sans-serif;\n        }\n        \n        .content-main {\n            font-size: clamp(1.1rem, 2.5vw, 1.4rem);\n            line-height: 1.9; \n            color: #3d3d56;\n        }\n        \n        .content-points {\n            list-style: none; padding: 0; margin: 25px 0 0 0;\n        }\n        \n        .content-points li {\n            margin-bottom: 25px; padding-left: 45px; position: relative; font-size: 1.25rem;\n        }\n        \n        .content-points li::before {\n            content: \"\";\n            position: absolute;\n            left: 5px; top: 10px;\n            width: 18px; height: 18px;\n            border-radius: 50%;\n            background: linear-gradient(45deg, #fbc2eb, #a6c1ee);\n            box-shadow: 0 0 10px rgba(176, 196, 222, 0.8);\n            filter: blur(1px);\n            transform: scale(1);\n            transition: all 0.3s ease;\n        }\n        .content-points li:hover::before {\n            transform: scale(1.3); filter: blur(0);\n        }\n\n        .slide-footer {\n            position: absolute;\n            bottom: 25px;\n            right: 40px;\n            font-size: 16px;\n            color: rgba(0, 0, 0, 0.4);\n            font-weight: 600;\n        }\n        \n        .stat-card {\n            background: rgba(255, 255, 255, 0.6);\n            padding: 25px 20px;\n            border-radius: 20px;\n            text-align: center;\n            border: none;\n            box-shadow: 0 8px 25px rgba(176, 196, 222, 0.5);\n            transition: all 0.4s ease-out;\n        }\n\n        .stat-card:hover {\n            transform: translateY(-10px) scale(1.03);\n            box-shadow: 0 15px 40px rgba(252, 92, 125, 0.4);\n        }\n        \n        .stat-number {\n            font-family: 'Playfair Display', serif;\n            font-size: 3.2rem;\n            font-weight: 600;\n            display: block;\n            background: linear-gradient(60deg, #fc5c7d, #6a82fb);\n            -webkit-background-clip: text;\n            -webkit-text-fill-color: transparent;\n        }\n        \n        .stat-label {\n            font-size: 1rem;\n            color: #6a788b;\n            margin-top: 5px;\n        }\n\n    </style>\n</head>\n<body>\n    <div class=\"background-canvas\"></div>\n    \n    <div class=\"slide-container\">\n        <div class=\"slide-header\">\n            <h1 class=\"slide-title\">{{ main_heading }}</h1>\n        </div>\n        \n        <div class=\"slide-content\">\n            <div class=\"content-main\">\n                {{ page_content }}\n            </div>\n        </div>\n        \n        <div class=\"slide-footer\">\n            {{ current_page_number }} / {{ total_page_count }}\n        </div>\n    </div>\n\n    <svg xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\" style=\"display:none\">\n        <defs>\n            <filter id=\"impressionist-turbulence\">\n                <feTurbulence type=\"fractalNoise\" baseFrequency=\"0.005 0.009\" numOctaves=\"4\" seed=\"3\" stitchTiles=\"stitch\"/>\n                <feDisplacementMap in2=\"turbulence\" in=\"SourceGraphic\" scale=\"80\" xChannelSelector=\"R\" yChannelSelector=\"G\" />\n                <feGaussianBlur in=\"SourceGraphic\" stdDeviation=\"3\" />\n            </filter>\n        </defs>\n    </svg>\n\n</body>\n</html>", "tags": ["莫奈", "印象派", "艺术", "光影", "油画", "色彩", "强烈"], "is_default": false, "export_info": {"exported_at": "2025-07-23T08:45:00.000Z", "original_id": null, "original_created_at": null}}