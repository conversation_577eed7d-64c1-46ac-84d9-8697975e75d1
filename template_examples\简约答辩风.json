{"template_name": "简约答辩风", "description": "以蓝白为主色调的大学答辩风格", "html_template": "<!DOCTYPE html>\n<html lang=\"zh-CN\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>{{ page_title }}</title>\n    <script src=\"https://cdn.tailwindcss.com\"></script>\n    <script src=\"https://cdn.jsdelivr.net/npm/chart.js\"></script>\n    <script src=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/js/all.min.js\"></script>\n    <style>\n        /* --- 基础与背景设置 --- */\n        html {\n            height: 100%;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            background-color: #f0f7ff;\n        }\n\n        body {\n            width: 1280px;\n            height: 720px;\n            margin: 0;\n            padding: 0;\n            position: relative;\n            overflow: hidden;\n            font-family: 'Inter', 'Microsoft YaHei', 'PingFang SC', sans-serif;\n            flex-shrink: 0;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n        }\n\n        body::before {\n            content: '';\n            position: absolute;\n            top: -20%; left: -20%; width: 140%; height: 140%;\n            background: \n                radial-gradient(circle at 20% 80%, rgba(173, 216, 230, 0.2), transparent 40%),\n                radial-gradient(circle at 75% 30%, rgba(135, 206, 250, 0.2), transparent 40%);\n            z-index: 1;\n            animation: liquid-flow-outer 30s ease-in-out infinite alternate;\n        }\n        \n        body::after {\n            content: '';\n            position: absolute;\n            width: 1280px;\n            height: 720px;\n            background: \n                radial-gradient(circle at 20% 80%, rgba(173, 216, 230, 0.25), transparent 40%),\n                radial-gradient(circle at 75% 30%, rgba(135, 206, 250, 0.25), transparent 40%);\n            z-index: 2;\n            border-radius: 30px;\n            animation: liquid-flow-inner 20s ease-in-out infinite alternate;\n        }\n\n        @keyframes liquid-flow-outer {\n            from { transform: rotate(0deg) scale(1.2); filter: blur(60px); }\n            to   { transform: rotate(180deg) scale(1.5); filter: blur(80px); }\n        }\n        \n        @keyframes liquid-flow-inner {\n            from { transform: rotate(0deg) scale(1); filter: blur(30px); }\n            to   { transform: rotate(-180deg) scale(1.2); filter: blur(40px); }\n        }\n\n        .slide-container {\n            width: 100%;\n            height: 100%;\n            display: flex;\n            flex-direction: column;\n            color: #001f3f;\n            position: relative;\n            z-index: 3; \n            background: rgba(255, 255, 255, 0.7);\n            backdrop-filter: blur(30px) saturate(120%);\n            -webkit-backdrop-filter: blur(30px) saturate(120%);\n            border-radius: 30px;\n            border: 1px solid rgba(0, 60, 120, 0.15);\n            box-shadow: \n                inset 0 0 1px 1px rgba(0, 100, 200, 0.1), \n                0 8px 30px rgba(0, 40, 100, 0.15); \n        }\n        \n        .slide-header {\n            padding: 40px 60px 20px 60px;\n            border-bottom: 1px solid rgba(0, 60, 120, 0.1);\n        }\n        \n        .slide-title {\n            font-size: clamp(2.2rem, 4vw, 3.2rem);\n            font-weight: 700; \n            color: #001f3f;\n            margin: 0;\n            line-height: 1.2;\n        }\n        \n        .slide-content {\n            flex: 1;\n            padding: 30px 60px;\n            display: flex;\n            flex-direction: column;\n            justify-content: center;\n            max-height: 580px;\n            overflow: auto;\n        }\n        \n        .content-main {\n            font-size: clamp(1.1rem, 2.5vw, 1.4rem);\n            line-height: 1.7; \n            color: #001f3f;\n        }\n        \n        .content-points {\n            list-style: none; padding: 0; margin: 20px 0 0 0;\n        }\n        \n        .content-points li {\n            margin-bottom: 20px; padding-left: 40px; position: relative; font-size: 1.2rem;\n        }\n\n        .content-points li:before {\n            content: \"\";\n            position: absolute;\n            left: 5px; top: 10px;\n            width: 12px; height: 12px;\n            border-radius: 50%;\n            background: radial-gradient(circle at 30% 30%, #0074D9, #7FDBFF);\n            box-shadow: \n                inset 0 0 2px rgba(0,0,0,0.2),\n                0 0 6px #7FDBFF;\n        }\n        \n        .slide-footer {\n            position: absolute;\n            bottom: 25px;\n            right: 40px;\n            font-size: 16px;\n            color: rgba(0, 60, 120, 0.6);\n            font-weight: 500;\n        }\n\n        .highlight-box {\n            background: rgba(224, 240, 255, 0.6);\n            border-left: 4px solid #0074D9;\n            padding: 18px 22px;\n            margin: 20px 0;\n            border-radius: 0 12px 12px 0;\n            backdrop-filter: blur(8px);\n            -webkit-backdrop-filter: blur(8px);\n        }\n        \n        .stats-grid {\n            display: grid;\n            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));\n            gap: 20px;\n            margin: 20px 0;\n        }\n\n        .stat-card {\n            background: rgba(255, 255, 255, 0.6);\n            padding: 20px 16px;\n            border-radius: 16px;\n            text-align: center;\n            border: 1px solid rgba(0, 90, 180, 0.1);\n            backdrop-filter: blur(10px);\n            transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);\n        }\n\n        .stat-card:hover {\n            transform: translateY(-5px) scale(1.02);\n            background: rgba(240, 248, 255, 0.8);\n            box-shadow: 0 6px 20px rgba(0, 60, 120, 0.15);\n            border-color: rgba(0, 100, 200, 0.25);\n        }\n        \n        .stat-number {\n            font-size: 2.4rem;\n            font-weight: 700;\n            color: #003366;\n            display: block;\n        }\n        \n        .stat-label {\n            font-size: 0.95rem;\n            color: #003366;\n            margin-top: 5px;\n        }\n        \n        @media (max-width: 1280px) {\n            body, body::after {\n                width: 95vw;\n                height: 53.4375vw;\n                max-height: 95vh;\n            }\n        }\n    </style>\n</head>\n<body>\n    <div class=\"slide-container\">\n        <div class=\"slide-header\">\n            <h1 class=\"slide-title\">{{ main_heading }}</h1>\n        </div>\n        \n        <div class=\"slide-content\">\n            <div class=\"content-main\">\n                {{ page_content }}\n            </div>\n        </div>\n        \n        <div class=\"slide-footer\">\n            {{ current_page_number }} / {{ total_page_count }}\n        </div>\n    </div>\n</body>\n</html>\n", "tags": ["蓝白", "大学答辩"], "is_default": false, "export_info": {"exported_at": "2025-07-23T07:58:37.924Z", "original_id": 10, "original_created_at": 1753257350.5712268}}