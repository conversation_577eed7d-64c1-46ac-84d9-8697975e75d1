"""
提示模板 - 定义所有LLM交互的提示模板
"""

from langchain_core.prompts import ChatPromptTemplate


class PromptTemplates:
    """提示模板集合"""
    
    @staticmethod
    def get_structure_analysis_prompt() -> ChatPromptTemplate:
        """文档结构分析提示"""
        return ChatPromptTemplate([
            ("human", """
            ## 任务目标
            基于提供的文档内容，执行结构化信息提取，生成标准化的文档元数据。
             
            ## 输入参数
            **项目基本信息：**
            - 项目主题：{project_topic}
            - 应用场景：{project_scenario}
            - 具体要求：{project_requirements}

            **目标受众信息：**
            - 受众类型：{target_audience}
            - 自定义受众：{custom_audience}

            **风格要求：**
            - PPT风格：{ppt_style}
            - 自定义风格提示：{custom_style_prompt}

            ## 输入文档
            {content}

            ## 核心约束条件
            1. **源材料依赖原则**：所有提取信息必须基于上述文档内容，可以理解和转化表达，但不得添加文档外信息
            2. **理解转化原则**：允许对源材料内容进行理解、归纳和专业化表达，但必须忠实于原意
            3. **数据准确原则**：定量数据（数字、百分比、统计值等）必须保持绝对准确，不得修改或估算

            ## 提取字段规范
            请按以下规范提取信息并输出JSON格式：

            ### 必需字段
            - **title** (string|null): 文档标题，仅当原文明确包含标题时提取，否则设为null
            - **type** (string): 文档类型，基于内容特征判断（学术论文/技术报告/商业文档/教程/其他）
            - **sections** (array): 章节标题列表，仅提取原文中明确的章节标题，无章节时设为空数组
            - **key_concepts** (array): 关键概念列表，仅提取原文中明确提及的专业术语和核心概念
            - **language** (string): 文档原始语言标识
            - **complexity** (string): 内容复杂度评级（简单/中等/复杂），基于实际内容难度
            - **key_data** (array): 关键数据集合，仅提取原文中的具体数值、百分比、统计指标
            - **main_conclusions** (array): 主要结论集合，仅提取原文中明确表述的结论性陈述

            ## 提取执行标准
            - **数据完整性**：key_data必须为原文中实际出现的精确数值，保持绝对准确
            - **结论理解性**：main_conclusions基于原文结论进行理解和专业化表达，保持核心观点一致
            - **结构优化性**：sections可以对原文章节进行合理整理和专业化表述，保持逻辑结构
            - **概念专业性**：key_concepts基于原文术语，可使用更专业和标准的表述方式

            ## 输出格式示例
            ```json
            {{
                "title": "深度学习在自然语言处理中的应用",
                "type": "学术论文",
                "sections": ["摘要", "引言", "相关工作", "方法论", "实验结果", "结论"],
                "key_concepts": ["深度学习", "自然语言处理", "神经网络", "机器学习"],
                "language": "中文",
                "complexity": "复杂",
                "key_data": ["准确率提升15%", "训练时间减少30%", "数据集包含10万条样本"],
                "main_conclusions": ["深度学习显著提升了NLP任务性能", "Transformer架构是当前最优选择"]
            }}
            ```

            ## 质量控制检查点
            输出前请执行以下验证：
            1. 每项提取信息是否基于原文内容并忠实于原意？
            2. 定量数据是否保持绝对准确？
            3. JSON格式是否符合规范要求？
            4. 数据类型是否与字段规范一致？

            请严格按照上述规范执行提取任务，确保输出结果的准确性和完整性。
            """)
        ])
    
    @staticmethod
    def get_initial_outline_prompt() -> ChatPromptTemplate:
        """初始PPT框架生成提示"""
        return ChatPromptTemplate([
            ("human", """
            ## 任务目标
            基于文档结构和内容，结合项目具体要求和目标受众，生成PPT演示文稿的初始框架大纲。

            ## 输入参数
            **项目基本信息：**
            - 项目主题：{project_topic}
            - 应用场景：{project_scenario}
            - 具体要求：{project_requirements}

            **目标受众信息：**
            - 受众类型：{target_audience}
            - 自定义受众：{custom_audience}

            **风格要求：**
            - PPT风格：{ppt_style}
            - 自定义风格提示：{custom_style_prompt}

            **文档结构元数据：**
            {structure}

            **源文档内容：**
            {content}

            **页数约束：**
            {slides_range}

            **目标语言：**
            {target_language}

            ## 核心约束条件
            ### 1. 源材料理解原则
            - 大纲内容必须基于上述文档结构和内容，通过理解和转化形成专业表达
            - 允许对源材料进行合理的理解、归纳、重组和专业化表述
            - 每个内容要点应忠实反映源材料的核心观点和信息
            - 可以进行逻辑整理和结构优化，但不得偏离原意

            ### 2. 内容转化标准
            - 数值数据：必须使用源材料中的原始数据，保持绝对精确性
            - 结论观点：基于源材料内容进行理解和专业化表达，保持核心观点一致
            - 术语概念：可以使用更专业、标准的术语表述，提升内容的专业性
            - 表达优化：鼓励使用更清晰、专业的语言重新组织和表达源材料内容

            ### 3. 语言表达规范
            - **书面化要求**：使用正式、规范的书面语言，避免口语化表达
            - **专业化标准**：采用学术或商务场合的专业术语和表达方式
            - **目标语言**：所有输出内容必须严格使用目标语言：{target_language}
            - **表达提升**：鼓励使用更清晰、准确、专业的语言重新表述源材料内容

            ### 4. 结构组织原则
            - **智能重组**：基于对源材料的理解，进行合理的层次化重组和逻辑优化
            - **结构创新**：可以创造性地调整内容组织结构，使其更适合PPT展示和受众理解
            - **逻辑提升**：通过重新组织提升内容的逻辑性和表达效果
            - **内容完整**：确保重组后涵盖源材料的所有重要信息和观点

            ### 5. 数据可视化原则
            - 图表配置：当源材料包含数值数据时，可以添加合适的chart_config提升展示效果
            - 数据准确：图表数据必须完全来自源材料，保持绝对准确性
            - 支持类型：包括但不限于柱状图(bar)、折线图(line)、饼图(pie)、环形图(doughnut)、雷达图(radar)等
            - 可视化优化：可以选择最适合的图表类型来展示数据，提升理解效果

            ## 项目信息应用指导
            ### 1. 主题和场景适配
            - **主题聚焦**：确保PPT大纲紧密围绕项目主题：{project_topic}
            - **场景适配**：根据应用场景（{project_scenario}）调整内容组织和表达方式
            - **要求整合**：将项目具体要求（{project_requirements}）融入大纲设计中

            ### 2. 目标受众定制
            - **受众适配**：根据目标受众类型（{target_audience}）调整内容深度和表达方式
            - **自定义考虑**：如有自定义受众描述（{custom_audience}），据此优化内容的专业程度和表达风格
            - **理解层次**：确保内容难度和表达方式适合目标受众的理解水平

            ### 3. 风格要求实现
            - **风格匹配**：根据PPT风格要求（{ppt_style}）调整内容的组织和表达方式
            - **自定义风格**：如有自定义风格提示（{custom_style_prompt}），在内容组织中体现相应的风格特征
            - **一致性保持**：确保整个大纲在风格上保持一致性

            ## 生成规范要求
            ### 1. 标题设计规范
            - PPT主标题必须基于文档的实际主题内容和项目主题，使用专业化表述
            - 采用符合目标受众和应用场景的标题表达方式
            - 使用目标语言进行准确表达：{target_language}
            - 保持与源材料主题和项目要求的一致性

            ### 2. 页数控制标准（最高优先级）
            - 【强制要求】严格遵守页数约束：{slides_range}
            - 【强制要求】页数约束是不可违反的硬性要求，必须优先满足
            - 如果内容不足以达到最小页数要求，必须通过以下方式扩展：
              * 添加更多的分析和解释
              * 增加相关的背景信息和延伸内容
              * 将复杂内容拆分为多个页面进行详细阐述

            ### 3. 结构组织框架
            - **标题页**：结合项目主题和文档内容，使用适合目标受众的标题表述
            - **目录页**：可对文档章节进行逻辑化重组，形成清晰的层次结构
            - **内容页**：基于文档内容，采用适合目标受众的专业化语言表达和逻辑组织
            - **结论页**：基于文档结论和项目要求，使用书面化的总结表述

            ## 内容生成执行标准
            ### 项目信息整合要求
            - **主题对齐**：确保PPT内容与项目主题（{project_topic}）高度一致
            - **场景适配**：根据应用场景（{project_scenario}）调整内容的组织方式和重点
            - **需求响应**：将项目具体要求（{project_requirements}）体现在内容选择和表达中
            - **受众定制**：根据目标受众（{target_audience}）和自定义受众描述（{custom_audience}）调整内容深度和表达方式
            - **风格体现**：根据PPT风格（{ppt_style}）和自定义风格提示（{custom_style_prompt}）调整内容组织和表达风格

            ### 语言表达要求
            - **书面化标准**：所有content_points必须使用正式的书面语言表达
            - **专业化程度**：采用适合目标受众的学术、商务或技术领域的专业表述方式
            - **表述优化**：在保持源材料原意的前提下，可对表达方式进行专业化改进
            - **术语规范**：使用标准化的专业术语，避免口语化表达
            - **受众适配**：确保语言复杂度和专业程度适合目标受众理解

            ### 结构组织要求
            - **层次化重组**：可对源材料内容进行合理的层次化重新组织
            - **逻辑优化**：调整内容顺序和结构，使其更符合演示逻辑和应用场景
            - **内容完整**：确保重组过程中不遗漏源材料的重要信息
            - **可视化条件**：图表配置仅在源材料包含具体数值数据时添加
            - **场景匹配**：确保结构组织符合应用场景的展示需求

            ## 输出格式规范
            请按以下JSON结构输出大纲：

            ### 根级字段
            - **title** (string): PPT主标题，基于文档实际内容，使用目标语言：{target_language}
            - **total_pages** (integer): 预计总页数
            - **page_count_mode** (string): 固定值"estimated"
            - **slides** (array): 幻灯片对象数组

            ### 幻灯片对象结构
            每个幻灯片对象包含以下字段：
            - **page_number** (integer): 页码序号
            - **title** (string): 幻灯片标题，基于源材料内容，使用专业化的书面表述，目标语言：{target_language}
            - **content_points** (array): 内容要点列表，基于源材料内容，使用正式的书面语言和专业表述
            - **slide_type** (string): 幻灯片类型（"title"/"content"/"conclusion"）
            - **description** (string): 幻灯片功能描述，使用专业化的描述语言，目标语言：{target_language}
            - **chart_config** (object, 可选): 图表配置对象，仅当源材料包含具体数据时添加

            ### 输出格式示例
            ```json
            {{
                "title": "[基于文档实际标题的准确表述]",
                "total_pages": 10,
                "page_count_mode": "estimated",
                "slides": [
                    {{
                        "page_number": 1,
                        "title": "[基于文档实际标题的准确表述]",
                        "content_points": ["[文档中的实际信息]", "[文档中的实际作者信息]", "[文档中的实际日期信息]"],
                        "slide_type": "title",
                        "description": "PPT标题页，展示文档核心主题信息"
                    }},
                    {{
                        "page_number": 2,
                        "title": "[基于文档实际章节标题]",
                        "content_points": ["[文档中的实际内容要点1]", "[文档中的实际数据]", "[文档中的实际结论]"],
                        "slide_type": "content",
                        "description": "基于文档实际内容的主体幻灯片"
                    }}
                ]
            }}
            ```

            ## 质量控制与验证
            ### 生成前验证检查点
            执行以下逐项验证：
            1. **内容理解检查**：每个content_points是否忠实反映了源材料的核心观点和信息
            2. **数据准确性检查**：所有数值、百分比是否保持源材料中的绝对准确性
            3. **标题合理性检查**：所有标题是否基于源材料内容并进行了专业化表述
            4. **内容充实检查**：内容是否通过理解和转化变得更加专业和清晰
            5. **可视化合理检查**：图表配置是否基于源材料数据并选择了合适的展示方式

            ### 最终质量检查清单
            输出前请确认以下要点：
            - [ ] 每个幻灯片标题是否基于源材料并进行了专业化表述？
            - [ ] 每个content_points是否忠实反映源材料核心内容？
            - [ ] 所有定量数据是否保持绝对准确？
            - [ ] 内容表达是否更加专业、清晰和有逻辑？
            - [ ] 所有内容是否统一使用目标语言：{target_language}？
            - [ ] JSON格式是否符合规范要求？

            ## 执行指令
            请严格按照上述规范和约束条件执行大纲生成任务，确保输出结果的准确性、完整性和规范性。
            """)
        ])
    
    @staticmethod
    def get_refine_outline_prompt() -> ChatPromptTemplate:
        """内容细化提示"""
        return ChatPromptTemplate([
            ("human", """
            ## 任务目标
            基于现有PPT大纲和新增文档内容，结合项目要求和目标受众，执行大纲细化和扩展操作。

            ## 输入参数
            **项目基本信息：**
            - 项目主题：{project_topic}
            - 应用场景：{project_scenario}
            - 具体要求：{project_requirements}

            **目标受众信息：**
            - 受众类型：{target_audience}
            - 自定义受众：{custom_audience}

            **风格要求：**
            - PPT风格：{ppt_style}
            - 自定义风格提示：{custom_style_prompt}

            **现有PPT结构：**
            {existing_outline}

            **新增文档内容：**
            {new_content}

            **累积上下文信息：**
            {context}

            **页数约束：**
            {slides_range}

            **目标语言：**
            {target_language}

            ## 核心约束条件
            ### 1. 源材料理解原则
            - 所有细化和扩展内容必须基于新增内容和累积上下文的深度理解
            - 允许对源材料进行合理的分析、归纳和专业化表述
            - 每个新增或修改的content_points应忠实反映源材料的核心观点
            - 鼓励通过理解和转化提升内容的专业性和表达效果

            ### 2. 内容转化标准
            - **数据完整性**：所有新增数值、百分比必须保持源材料中的绝对准确性
            - **观点提升性**：基于源材料内容进行理解和专业化表达，提升观点的清晰度
            - **现有内容优化**：在保持准确性的基础上，优化现有内容的表达和组织
            - **智能分析**：允许基于源材料进行合理的分析和洞察，提升内容深度

            ### 3. 语言表达规范
            - **书面化要求**：所有新增和修改内容必须使用正式的书面语言
            - **专业化标准**：采用专业领域的标准表述方式和术语
            - **目标语言**：所有输出内容必须严格使用目标语言：{target_language}
            - **风格一致性**：保持新旧内容在专业化语言风格上的一致性
            - **表达提升**：鼓励通过理解和转化，使表达更加清晰、专业和有说服力

            ### 4. 数据可视化原则
            - **智能配置**：当新增内容包含数值数据时，可以添加或优化chart_config
            - **数据准确**：图表数据必须保持源材料的绝对准确性
            - **效果优化**：可以选择最佳的可视化方式来提升数据展示效果

            ## 细化任务执行标准
            ### 1. 内容理解分析
            - **深度理解**：深入分析新增内容的核心观点、逻辑结构和关键信息
            - **智能提取**：基于理解提取最有价值的信息点和洞察

            ### 2. 内容智能更新
            - **理解更新**：基于对新增内容的理解，智能更新和优化现有幻灯片
            - **表达提升**：通过专业化表述提升内容的清晰度和说服力

            ### 3. 幻灯片扩展优化
            - **价值导向**：当新增内容具有独立价值时，创建新的幻灯片展示
            - **逻辑完整**：确保扩展后的结构更加完整和有逻辑

            ### 4. 逻辑连贯性提升
            - **结构优化**：基于对内容的理解，优化整体逻辑结构
            - **关联强化**：加强新旧内容之间的逻辑关联和过渡

            ### 5. 内容分布优化
            - **智能分配**：基于内容重要性和逻辑关系进行智能分配
            - **平衡提升**：通过重新组织实现更好的内容平衡

            ### 6. 可视化智能配置
            - **数据展示**：为源材料中的数据选择最佳的可视化方式
            - **效果优化**：通过合理的图表配置提升数据展示效果

            ## 细化执行原则
            ### 项目信息整合
            - **主题一致性**：确保细化内容与项目主题（{project_topic}）保持高度一致
            - **场景适配性**：根据应用场景（{project_scenario}）调整细化内容的重点和表达方式
            - **需求响应性**：将项目具体要求（{project_requirements}）体现在内容细化中
            - **受众定制化**：根据目标受众（{target_audience}）和自定义受众（{custom_audience}）调整内容深度
            - **风格统一性**：确保细化内容符合PPT风格（{ppt_style}）和自定义风格要求（{custom_style_prompt}）

            ### 内容细化标准
            - **智能重组**：基于对内容的深度理解，进行创新性的结构调整和层次化重组
            - **逻辑强化**：通过理解内容间的内在联系，强化逻辑关联和表达效果
            - **语言提升**：确保新增内容在专业性和表达质量上达到更高标准
            - **表达创新**：鼓励通过创新性表述提升内容的影响力和说服力
            - **价值最大化**：通过智能分析和重组，最大化内容的展示价值
            - **结构完善**：基于内容理解，创建更完善和有逻辑的整体结构

            ## 输出格式规范
            请返回完整的JSON格式PPT大纲，包含以下字段：
            - **title** (string): 更新后的PPT标题，基于源材料内容，使用目标语言：{target_language}
            - **total_pages** (integer): 更新后的总页数
            - **page_count_mode** (string): 固定值"refining"
            - **slides** (array): 完整的幻灯片对象列表，严格基于源材料内容

            ## 质量控制与验证
            ### 生成前验证检查点
            执行以下逐项验证：
            1. **内容理解检查**：每个新增或修改的content_points是否忠实反映源材料的核心观点？
            2. **智能分析验证**：基于源材料的分析和洞察是否合理且有价值？
            3. **数据准确性检查**：所有定量数据是否保持源材料的绝对准确性？
            4. **可视化优化检查**：图表配置是否基于源材料数据并选择了最佳展示方式？

            ### 最终质量检查清单
            输出前请确认以下要点：
            - [ ] 新增内容是否基于源材料并进行了智能理解和转化？
            - [ ] 修改内容是否在保持准确性的基础上提升了表达质量？
            - [ ] 是否通过理解和分析提升了内容的专业性和价值？
            - [ ] 所有内容是否统一使用目标语言：{target_language}？
            - [ ] JSON格式是否符合规范要求？
            - [ ] 新旧内容的逻辑连贯性是否得到了优化和提升？

            ## 执行指令
            请严格按照上述规范和约束条件执行大纲细化任务，确保输出结果的准确性、完整性和规范性。
            """)
        ])
    
    @staticmethod
    def get_finalize_outline_prompt() -> ChatPromptTemplate:
        """最终优化提示"""
        return ChatPromptTemplate([
            ("human", """
            ## 任务目标
            对PPT大纲执行最终优化处理，结合项目要求和目标受众，确保输出质量达到发布标准。

            ## 输入参数
            **项目基本信息：**
            - 项目主题：{project_topic}
            - 应用场景：{project_scenario}
            - 具体要求：{project_requirements}

            **目标受众信息：**
            - 受众类型：{target_audience}
            - 自定义受众：{custom_audience}

            **风格要求：**
            - PPT风格：{ppt_style}
            - 自定义风格提示：{custom_style_prompt}

            **当前大纲：**
            {outline}

            **页数约束：**
            {slides_range}

            **目标语言：**
            {target_language}

            ## 核心约束条件
            ### 0. 页数约束（最高优先级）
            - 【强制要求】必须严格遵守页数约束：{slides_range}
            - 【强制要求】如果当前大纲页数不符合要求，必须通过以下方式调整：
              * 页数不足时：增加详细内容、延伸讨论等
              * 页数过多时：合并相似内容、精简表述、整合相关主题
            - 【强制要求】页数约束优先级高于内容质量，必须首先满足页数要求

            ### 1. 项目信息最终整合
            - **主题契合度**：确保最终大纲与项目主题（{project_topic}）完全契合
            - **场景适配度**：验证内容组织是否完全适配应用场景（{project_scenario}）
            - **需求满足度**：检查是否充分响应了项目具体要求（{project_requirements}）
            - **受众匹配度**：确保内容深度和表达方式完全适合目标受众（{target_audience}、{custom_audience}）
            - **风格一致性**：验证整体风格是否符合PPT风格要求（{ppt_style}、{custom_style_prompt}）

            ### 2. 内容优化原则
            - **智能优化**：基于对源材料的深度理解，进行全面的内容优化和提升
            - **价值增强**：通过专业化表述和逻辑重组，增强内容的价值和影响力
            - **创新表达**：鼓励在忠实于原意的基础上，采用更创新和有效的表达方式
            - **理解转化**：将源材料内容转化为更专业、更有说服力的表述

            ### 3. 内容完整性标准
            - **价值覆盖**：确保覆盖源材料的所有重要价值点，通过理解提升表达效果
            - **质量提升**：验证每个幻灯片内容是否在忠实反映原意的基础上提升了质量
            - **数据准确性**：所有数值、百分比必须与源材料保持绝对一致
            - **观点优化**：基于源材料的结论和观点，进行专业化和清晰化表述

            ### 4. 语言规范要求
            - 确保所有内容统一使用目标语言：{target_language}
            - 保持术语翻译的一致性和准确性
            - 提升语言表达的专业性和影响力

            ### 5. 数据可视化原则
            - **智能配置**：为源材料中的数据选择最佳的图表配置和展示方式
            - **数据准确性**：图表数据必须与源材料中的数据保持绝对一致
            - **效果优化**：通过合理的可视化设计提升数据展示效果和理解度

            ### 5. 布局控制标准
            - **内容点数量**：每页内容点严格控制在3-6个范围内
            - **要点长度**：基于源材料内容，控制在合理的表达长度
            - **分布原则**：内容分布基于源材料的实际结构和重要性

            ## 优化任务执行标准
            ### 1. 幻灯片类型分布优化
            基于源材料的实际结构进行类型分配：
            - **title**: 标题页，基于源材料的核心主题
            - **content**: 内容页，基于源材料的章节和实际内容
            - **conclusion**: 结论页，基于源材料中的实际结论性内容

            ### 2. 内容要点智能优化
            - **理解深化**：基于对源材料的深度理解，提升内容的准确性和完整性
            - **表达创新**：使用更专业、更有影响力的语言重新表述源材料内容
            - **专业化升级**：采用行业领先的术语和表述方式，提升专业水准
            - **价值放大**：通过精炼和优化，放大内容的价值和说服力
            - **智能转化**：将源材料转化为更适合目标受众的专业表述
            - **洞察提升**：基于源材料进行合理的分析和洞察，增强内容深度

            ### 3. 逻辑流程结构调整
            - **结构优化**：可对源材料内容进行合理的逻辑重组，提升演示效果
            - **层次清晰**：建立清晰的层次结构，使内容更适合PPT展示
            - **逻辑连贯**：确保调整后的结构逻辑连贯，便于理解
            - **重点突出**：重点突出源材料中明确强调的内容，使用专业化表述

            ### 4. 语言表达规范化
            - **书面化标准**：确保所有内容使用正式的书面语言表达
            - **专业化程度**：采用学术或商务场合的专业表述方式
            - **目标语言**：确保所有内容使用目标语言：{target_language}
            - **表达一致性**：保持整个大纲在专业化语言风格上的一致性
            - **术语规范**：使用标准化的专业术语，避免口语化表达
            - **表述优化**：在保持原意的前提下，使表达更加正式和专业

            ### 5. 内容容量控制
            - **分配原则**：基于源材料的实际内容量进行合理分配
            - **平衡性考虑**：确保各幻灯片间内容量的适当平衡

            ### 6. 过渡和总结优化
            - **内容依据**：仅基于源材料中的实际过渡和总结内容
            - **连贯性保持**：确保过渡内容的逻辑连贯性，使用专业化表述

            ### 7. 页码序列管理
            - **顺序正确性**：确保页码的正确顺序和连续性
            - **编号规范性**：遵循标准的页码编号规范

            ### 8. 图表配置完善
            - **数据条件**：仅为源材料中的实际数据完善图表配置
            - **准确性要求**：确保图表准确反映源材料中的数据
            - **保真原则**：严禁修改或美化源材料中的数据

            ## 输出格式规范
            请输出最终的JSON格式PPT大纲，包含以下字段：
            - **title** (string): 最终PPT标题，基于源材料内容
            - **total_pages** (integer): 最终总页数
            - **page_count_mode** (string): 固定值"final"
            - **slides** (array): 优化后的完整幻灯片对象列表，严格基于源材料内容

            ## 质量控制与验证
            ### 生成前验证检查点
            执行以下逐项验证：
            1. **内容理解检查**：每个幻灯片内容是否忠实反映源材料的核心价值和观点？
            2. **优化效果验证**：内容优化是否在保持准确性的基础上提升了表达质量？
            3. **数据准确性检查**：所有定量数据是否与源材料保持绝对一致？
            4. **结构优化检查**：幻灯片结构是否通过理解和重组得到了优化？

            ### 最终质量检查清单
            输出前请确认以下要点：
            - [ ] **内容价值性**：是否基于源材料并通过理解转化提升了价值？
            - [ ] **完整性优化**：是否覆盖了源材料的主要内容并进行了合理优化？
            - [ ] **表达专业性**：是否通过专业化表述提升了内容质量？
            - [ ] **语言一致性**：是否统一使用目标语言：{target_language}？
            - [ ] **页数符合性**：【强制检查】是否严格符合页数约束：{slides_range}？如果不符合，必须调整页数
            - [ ] **JSON规范性**：是否符合JSON格式规范要求？
            - [ ] **逻辑优化性**：幻灯片间的逻辑关系是否得到了优化和提升？

            ## 执行指令
            请严格按照上述规范和约束条件执行最终优化任务，确保输出结果达到发布质量标准。
            """)
        ])
    
    @staticmethod
    def get_custom_prompt(template: str) -> ChatPromptTemplate:
        """自定义提示模板"""
        return ChatPromptTemplate([("human", template)])
    
    @staticmethod
    def get_error_recovery_prompt() -> ChatPromptTemplate:
        """错误恢复提示"""
        return ChatPromptTemplate([
            ("human", """
            ## 任务目标
            执行错误恢复操作，基于文档内容摘要，结合项目要求和目标受众，生成基础PPT大纲。

            ## 输入参数
            **项目基本信息：**
            - 项目主题：{project_topic}
            - 应用场景：{project_scenario}
            - 具体要求：{project_requirements}

            **目标受众信息：**
            - 受众类型：{target_audience}
            - 自定义受众：{custom_audience}

            **风格要求：**
            - PPT风格：{ppt_style}
            - 自定义风格提示：{custom_style_prompt}

            **文档内容摘要：**
            {content_summary}

            **错误信息：**
            {error_info}

            **目标语言：**
            {target_language}

            ## 核心约束条件
            ### 1. 源材料理解原则
            - **内容基础**：所有内容必须基于上述文档内容摘要的深度理解
            - **智能转化**：允许对摘要内容进行合理的理解、分析和专业化表述
            - **价值提取**：基于摘要内容提取最有价值的观点和信息
            - **创新表达**：鼓励通过理解和转化，创造更专业和有效的表达

            ### 2. 内容优化标准
            - **数据准确性**：所有数值、百分比必须保持摘要中的绝对准确性
            - **观点提升**：基于摘要的结论和观点，进行专业化和清晰化表述
            - **智能分析**：基于摘要内容进行合理的分析和洞察，提升内容价值
            - **质量优化**：通过理解和重组，优化内容质量和表达效果

            ### 3. 语言表达规范
            - **书面化要求**：所有输出内容必须使用正式的书面语言
            - **专业化标准**：采用专业领域的标准表述方式和术语
            - **目标语言**：所有输出内容必须使用目标语言：{target_language}
            - **表达创新**：鼓励通过创新性表述提升内容的专业性和影响力
            - **风格提升**：确保语言表达达到高水准的专业性和一致性

            ### 4. 项目信息应用原则
            - **主题对齐**：确保恢复的大纲与项目主题（{project_topic}）保持一致
            - **场景适配**：根据应用场景（{project_scenario}）调整内容组织方式
            - **需求体现**：在可能的范围内体现项目具体要求（{project_requirements}）
            - **受众考虑**：根据目标受众（{target_audience}、{custom_audience}）调整内容深度
            - **风格匹配**：尽可能体现PPT风格要求（{ppt_style}、{custom_style_prompt}）

            ### 5. 数据可视化原则
            - **智能配置**：当摘要包含数值数据时，选择最佳的图表配置方式
            - **数据准确**：图表数据必须保持摘要中的绝对准确性
            - **效果优化**：通过合理的可视化设计提升数据展示效果

            ## 生成规范要求
            请生成一个简洁但完整的PPT大纲，结合项目信息，包含以下结构：

            ### 基本结构组成
            1. **标题页**：结合项目主题和摘要中的实际主题信息
            2. **目录页**：基于摘要中的实际章节结构，适配应用场景
            3. **主要内容页**：3-5个，严格基于摘要中的实际内容，适合目标受众
            4. **结论页**：基于摘要中的实际结论内容，体现项目要求

            ### 幻灯片规范标准
            每个幻灯片必须满足以下要求：
            - **内容要点数量**：3-6个，严格来自摘要中的实际内容
            - **信息溯源性**：所有信息必须能在摘要中找到明确对应
            - **语言表达**：使用正式的书面语言和专业化表述
            - **可视化条件**：仅为摘要中的实际数据添加图表配置
            - **长度控制**：严格控制内容长度，确保布局合理性
            - **内容价值性**：基于摘要内容，通过理解和转化提升表达的价值和效果

            ## 质量控制与验证
            ### 生成前验证检查点
            执行以下逐项验证：
            1. **内容理解检查**：每个content_points是否忠实反映摘要的核心观点和价值？
            2. **智能转化检查**：是否通过理解和分析提升了内容的专业性和表达效果？
            3. **数据准确性检查**：所有定量数据是否保持摘要的绝对准确性？
            4. **结构优化检查**：大纲结构是否完整且经过了合理的优化？

            ## 输出格式要求
            返回标准JSON格式，确保结构完整且严格基于摘要内容。所有字段必须符合规范要求，确保后续处理的兼容性。

            ## 执行指令
            请严格按照上述规范执行错误恢复任务，生成可靠的基础PPT大纲。
            """)
        ])
    
    @classmethod
    def get_all_prompts(cls) -> dict:
        """获取所有提示模板"""
        return {
            "structure_analysis": cls.get_structure_analysis_prompt(),
            "initial_outline": cls.get_initial_outline_prompt(),
            "refine_outline": cls.get_refine_outline_prompt(),
            "finalize_outline": cls.get_finalize_outline_prompt(),
            "error_recovery": cls.get_error_recovery_prompt(),
        }
