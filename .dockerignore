# Git
.git
.gitignore
.gitattributes

# Docker
Dockerfile*
docker-compose*
.dockerignore

# Documentation
docs/

# IDE and Editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Testing
.tox/
.coverage
.coverage.*
.cache
.pytest_cache/
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# Environments
.env.local
.env.development
.env.test
.env.production

# Database files (will be created in container)
*.db
*.sqlite
*.sqlite3

# Logs
*.log
logs/

# Temporary files
temp/
tmp/
*.tmp

# Cache directories
.cache/
*_cache/

# Research reports (will be generated in container)
research_reports/

# Uploads (will be handled by volumes)
uploads/

# Node modules (if any)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Lock files (keep uv.lock for dependency resolution)
# uv.lock is kept for proper dependency installation

# Keep Docker-related scripts
!docker-healthcheck.sh
!docker-entrypoint.sh
