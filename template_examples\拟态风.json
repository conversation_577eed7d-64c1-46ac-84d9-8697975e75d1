{"template_name": "拟态风格", "description": "拟态风格", "html_template": "<!DOCTYPE html>\n<html lang=\"zh-CN\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>{{ page_title }}</title>\n    <!-- 移除了原有的外部库，因为此模板的样式是自包含的 -->\n    <style>\n        /* --- 核心：拟态风格设计变量 --- */\n        :root {\n            --neumorphic-bg-color: #e0e5ec;\n            --neumorphic-text-color-primary: #3b4b61;\n            --neumorphic-text-color-secondary: #7e8a9c;\n            --neumorphic-highlight-color: #4a76a8;\n            --neumorphic-shadow-dark: #a3b1c6;\n            --neumorphic-shadow-light: #ffffff;\n        }\n\n        html {\n            height: 100%;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            background-color: var(--neumorphic-bg-color); \n        }\n\n        body {\n            width: 1280px;\n            height: 720px;\n            margin: 0;\n            padding: 0;\n            position: relative;\n            overflow: hidden;\n            background-color: var(--neumorphic-bg-color);\n            font-family: 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', Arial, sans-serif;\n            flex-shrink: 0; \n        }\n        \n        /* --- 主要幻灯片容器：凸出效果 --- */\n        .slide-container {\n            width: 100%;\n            height: 100%;\n            display: flex;\n            flex-direction: column;\n            color: var(--neumorphic-text-color-primary);\n            position: relative;\n            border-radius: 40px;\n            background: var(--neumorphic-bg-color);\n            /* 关键：外凸阴影效果 */\n            box-shadow: 12px 12px 24px var(--neumorphic-shadow-dark),\n                        -12px -12px 24px var(--neumorphic-shadow-light);\n        }\n        \n        /* --- 幻灯片头部 --- */\n        .slide-header {\n            padding: 40px 60px 20px 60px;\n            /* 移除了边框，使用空间分隔 */\n        }\n        \n        .slide-title {\n            font-size: clamp(2.2rem, 4vw, 3.8rem);\n            font-weight: 700;\n            color: var(--neumorphic-text-color-primary);\n            margin: 0;\n            line-height: 1.2;\n            text-shadow: 1px 1px 2px var(--neumorphic-shadow-light);\n        }\n        \n        /* --- 幻灯片内容区 --- */\n        .slide-content {\n            flex: 1;\n            padding: 30px 60px;\n            display: flex;\n            flex-direction: column;\n            justify-content: center;\n            max-height: 580px;\n            overflow: hidden;\n        }\n        \n        .content-main {\n            font-size: clamp(1rem, 2.5vw, 1.5rem);\n            line-height: 1.6;\n            color: var(--neumorphic-text-color-secondary);\n        }\n        \n        /* --- 列表项：凹陷效果的图标 --- */\n        .content-points {\n            list-style: none;\n            padding: 0;\n            margin: 20px 0 0 0;\n        }\n        \n        .content-points li {\n            margin-bottom: 20px;\n            padding-left: 35px;\n            position: relative;\n            font-size: 1.4rem;\n            color: var(--neumorphic-text-color-primary);\n        }\n        \n        .content-points li:before {\n            content: \"\";\n            position: absolute;\n            left: 5px;\n            top: 50%;\n            transform: translateY(-50%);\n            width: 12px;\n            height: 12px;\n            border-radius: 50%;\n            background: var(--neumorphic-bg-color);\n            /* 关键：内凹/雕刻阴影效果 */\n            box-shadow: inset 3px 3px 6px var(--neumorphic-shadow-dark),\n                        inset -3px -3px 6px var(--neumorphic-shadow-light);\n        }\n        \n        /* --- 幻灯片页脚 --- */\n        .slide-footer {\n            position: absolute;\n            bottom: 25px;\n            right: 40px;\n            font-size: 16px;\n            color: var(--neumorphic-text-color-secondary);\n            font-weight: 600;\n        }\n\n        /* --- 拟态风格组件示例（可选） --- */\n\n        /* 示例：凹陷效果的容器 */\n        .highlight-box-inset {\n            background: var(--neumorphic-bg-color);\n            padding: 25px;\n            margin: 20px 0;\n            border-radius: 20px;\n            box-shadow: inset 5px 5px 10px var(--neumorphic-shadow-dark),\n                        inset -5px -5px 10px var(--neumorphic-shadow-light);\n        }\n        \n        /* 示例：凸起效果的统计卡片 */\n        .stats-grid {\n            display: grid;\n            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n            gap: 30px;\n            margin: 20px 0;\n        }\n        \n        .stat-card {\n            background: var(--neumorphic-bg-color);\n            padding: 25px;\n            border-radius: 20px;\n            text-align: center;\n            box-shadow: 7px 7px 14px var(--neumorphic-shadow-dark),\n                        -7px -7px 14px var(--neumorphic-shadow-light);\n            transition: all 0.2s ease-in-out;\n        }\n\n        .stat-card:hover {\n            /* 鼠标悬浮时变为轻微凹陷效果 */\n            box-shadow: inset 3px 3px 6px var(--neumorphic-shadow-dark),\n                        inset -3px -3px 6px var(--neumorphic-shadow-light);\n        }\n        \n        .stat-number {\n            font-size: 3rem;\n            font-weight: bold;\n            color: var(--neumorphic-highlight-color);\n            display: block;\n            text-shadow: 1px 1px 1px var(--neumorphic-shadow-light);\n        }\n        \n        .stat-label {\n            font-size: 1rem;\n            color: var(--neumorphic-text-color-secondary);\n            margin-top: 8px;\n        }\n        \n        /* --- 响应式设计 --- */\n        @media (max-width: 1280px) {\n            body {\n                width: 100vw;\n                height: 56.25vw; /* 保持16:9比例 */\n                max-height: 100vh;\n            }\n            .slide-container {\n                border-radius: 0; /* 在小屏幕上，通常铺满，无需圆角 */\n                box-shadow: none;  /* 也不需要阴影 */\n            }\n        }\n    </style>\n</head>\n<body>\n    <div class=\"slide-container\">\n        <header class=\"slide-header\">\n            <h1 class=\"slide-title\">{{ main_heading }}</h1>\n        </header>\n        \n        <main class=\"slide-content\">\n            <div class=\"content-main\">\n                <!-- 您可以将内容直接放在这里 -->\n                {{ page_content }}\n\n                <!-- 或者使用下面的列表结构 -->\n                <!--\n                <ul class=\"content-points\">\n                    <li>这是第一个要点，展示了拟态风格列表。</li>\n                    <li>每个要点前面都有一个凹陷的小圆点。</li>\n                    <li>设计简洁，注重质感和光影效果。</li>\n                </ul>\n                -->\n\n                <!-- 或者使用下面的卡片网格 -->\n                <!--\n                <div class=\"stats-grid\">\n                    <div class=\"stat-card\">\n                        <span class=\"stat-number\">75%</span>\n                        <span class=\"stat-label\">完成率</span>\n                    </div>\n                    <div class=\"stat-card\">\n                        <span class=\"stat-number\">1,200</span>\n                        <span class=\"stat-label\">活跃用户</span>\n                    </div>\n                    <div class=\"stat-card\">\n                        <span class=\"stat-number\">2.1M</span>\n                        <span class=\"stat-label\">总收入</span>\n                    </div>\n                </div>\n                -->\n            </div>\n        </main>\n        \n        <footer class=\"slide-footer\">\n            {{ current_page_number }} / {{ total_page_count }}\n        </footer>\n    </div>\n</body>\n</html>", "tags": ["拟态风格", "现代"], "is_default": false, "export_info": {"exported_at": "2025-06-28T10:13:53.314Z", "original_id": 3, "original_created_at": 1749556906.3194396}}